<template>
  <div class="ai-analysis card-container">
    <div class="analysis-header">
      <div class="analysis-title">🤖 AI智能解析</div>
      <div class="analysis-result" :class="resultClass">
        <span class="result-icon">{{ resultIcon }}</span>
        <span class="result-text">{{ resultText }}</span>
      </div>
    </div>

    <div class="analysis-content">
      <!-- 加载状态 -->
      <div v-if="loading" class="analysis-loading">
        <van-loading type="spinner" color="#1989fa" />
        <span>AI正在分析中...</span>
      </div>

      <!-- 解析内容 -->
      <template v-else-if="analysisData">
        <!-- 正确答案 -->
        <div class="analysis-section">
          <div class="section-title">正确答案</div>
          <div class="section-content">
            <span class="correct-answer">{{ correctAnswerText }}</span>
          </div>
        </div>

        <!-- AI解析内容 -->
        <div class="analysis-section">
          <div class="section-title">AI智能解析</div>
          <div class="section-content">
            <div
              class="ai-analysis-content"
              :class="{ 'streaming': loading || streamingContent }"
              v-html="formatAnalysisContent"
            ></div>
          </div>
        </div>

        <!-- 知识点标签 -->
        <div class="analysis-section" v-if="knowledgePoints.length > 0">
          <div class="section-title">相关知识点</div>
          <div class="section-content">
            <div class="knowledge-tags">
              <span
                v-for="point in knowledgePoints"
                :key="point"
                class="knowledge-tag"
              >
                {{ point }}
              </span>
            </div>
          </div>
        </div>
      </template>

      <!-- 默认内容（无AI解析时） -->
      <template v-else>
        <!-- 正确答案 -->
        <div class="analysis-section">
          <div class="section-title">正确答案</div>
          <div class="section-content">
            <span class="correct-answer">{{ correctAnswerText }}</span>
          </div>
        </div>

        <!-- 默认解析 -->
        <div class="analysis-section">
          <div class="section-title">答案解析</div>
          <div class="section-content">
            <p class="explanation-text">{{ explanationText }}</p>
          </div>
        </div>

        <!-- 知识点标签 -->
        <div class="analysis-section" v-if="knowledgePoints.length > 0">
          <div class="section-title">相关知识点</div>
          <div class="section-content">
            <div class="knowledge-tags">
              <span
                v-for="point in knowledgePoints"
                :key="point"
                class="knowledge-tag"
              >
                {{ point }}
              </span>
            </div>
          </div>
        </div>

        <!-- 错误分析 (仅错误时显示) -->
        <div class="analysis-section" v-if="!isCorrect && errorAnalysis">
          <div class="section-title">错误分析</div>
          <div class="section-content">
            <p class="error-text">{{ errorAnalysis }}</p>
          </div>
        </div>
      </template>
    </div>

    <!-- 关闭按钮 -->
    <div class="analysis-actions">
      <van-button 
        type="primary" 
        size="small" 
        @click="$emit('close')"
      >
        知道了
      </van-button>
    </div>
  </div>
</template>

<script setup>
import { computed } from 'vue'

const props = defineProps({
  question: {
    type: Object,
    default: () => ({})
  },
  userAnswer: {
    type: Array,
    default: () => []
  },
  isCorrect: {
    type: Boolean,
    default: false
  },
  analysisData: {
    type: Object,
    default: () => null
  },
  loading: {
    type: Boolean,
    default: false
  },
  streamingContent: {
    type: String,
    default: ''
  }
})

const emit = defineEmits(['close'])

// 计算属性
const resultClass = computed(() => {
  return props.isCorrect ? 'correct' : 'incorrect'
})

const resultIcon = computed(() => {
  return props.isCorrect ? '✅' : '❌'
})

const resultText = computed(() => {
  return props.isCorrect ? '回答正确' : '回答错误'
})

// 格式化AI解析内容
const formatAnalysisContent = computed(() => {
  // 优先使用流式内容，确保响应式更新
  const content = props.streamingContent || ''
  
  if (content) {
    return content.replace(/\n/g, '<br>')
  }

  // 如果没有流式内容，使用analysisData
  if (!props.analysisData || !props.analysisData.outputs) {
    return '暂无解析内容'
  }

  // 从LLM响应中提取解析内容
  const outputs = props.analysisData.outputs
  let resultContent = ''

  // 尝试从不同可能的字段获取内容
  if (outputs.text) {
    resultContent = outputs.text
  } else if (outputs.analysis) {
    resultContent = outputs.analysis
  } else if (outputs.result) {
    resultContent = outputs.result
  } else {
    // 如果没有找到标准字段，尝试获取第一个字符串值
    const firstStringValue = Object.values(outputs).find(value => typeof value === 'string')
    resultContent = firstStringValue || '暂无解析内容'
  }

  // 将换行符转换为HTML换行
  return resultContent.replace(/\n/g, '<br>')
})

const correctAnswerText = computed(() => {
  // 从题目数据获取正确答案
  if (props.question.correctAnswers && props.question.correctAnswers.length > 0) {
    return props.question.correctAnswers.join(', ')
  }
  // 从题目选项中获取正确答案
  if (props.question.options && props.question.options.length > 0) {
    const correctOptions = props.question.options.filter(opt =>
      props.question.correctAnswers?.includes(opt.value)
    )
    if (correctOptions.length > 0) {
      return correctOptions.map(opt => `${opt.label}. ${opt.text}`).join(', ')
    }
  }
  return '暂无正确答案信息'
})

const explanationText = computed(() => {
  // 从题目数据获取解析内容
  return props.question.explanation || '暂无解析内容'
})

const knowledgePoints = computed(() => {
  // 从题目数据获取知识点
  if (props.question.knowledgePoints && props.question.knowledgePoints.length > 0) {
    return props.question.knowledgePoints
  }
  return []
})

const errorAnalysis = computed(() => {
  if (props.isCorrect) return ''
  // 从AI解析数据中获取错误分析，如果没有则不显示
  return ''
})

// 记录用户查看解析行为
const recordAnalysisView = async (questionId) => {
  try {
    // 记录用户查看解析的行为，用于数据分析
    console.log('用户查看解析:', { questionId, timestamp: new Date() })
    // 这里可以添加实际的记录逻辑，比如发送到后端统计接口
  } catch (error) {
    console.error('记录解析查看失败:', error)
  }
}
</script>

<style scoped>
.ai-analysis {
  margin: 16px;
  background: #fff;
  border-radius: 12px;
  overflow: hidden;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.04);
}

.analysis-header {
  padding: 16px;
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  color: #fff;
}

.analysis-title {
  font-size: 16px;
  font-weight: 600;
  margin-bottom: 8px;
}

.analysis-result {
  display: flex;
  align-items: center;
  gap: 8px;
}

.result-icon {
  font-size: 18px;
}

.result-text {
  font-size: 14px;
  font-weight: 500;
}

.analysis-result.correct {
  color: #07c160;
}

.analysis-result.incorrect {
  color: #ff4444;
}

.analysis-content {
  padding: 16px;
}

.analysis-section {
  margin-bottom: 16px;
}

.analysis-section:last-child {
  margin-bottom: 0;
}

.section-title {
  font-size: 14px;
  font-weight: 600;
  color: #323233;
  margin-bottom: 8px;
}

.section-content {
  font-size: 14px;
  line-height: 1.6;
  color: #646566;
}

.correct-answer {
  display: inline-block;
  padding: 4px 8px;
  background: #e8f5e8;
  color: #07c160;
  border-radius: 4px;
  font-weight: 500;
}

.explanation-text {
  margin: 0;
}

.knowledge-tags {
  display: flex;
  flex-wrap: wrap;
  gap: 8px;
}

.knowledge-tag {
  display: inline-block;
  padding: 4px 8px;
  background: #e8f4fd;
  color: #1989fa;
  font-size: 12px;
  border-radius: 12px;
}

.error-text {
  margin: 0;
  color: #ff4444;
  background: #fff2f2;
  padding: 8px;
  border-radius: 4px;
  border-left: 3px solid #ff4444;
}

.analysis-actions {
  padding: 16px;
  border-top: 1px solid #ebedf0;
  text-align: center;
}

.analysis-loading {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  padding: 40px 20px;
  color: #666;
}

.analysis-loading span {
  margin-top: 12px;
  font-size: 14px;
}

.ai-analysis-content {
  line-height: 1.6;
  color: #333;
  min-height: 20px;
  position: relative;
}

/* 流式内容的打字机效果 */
.ai-analysis-content.streaming::after {
  content: '';
  display: inline-block;
  width: 2px;
  height: 1.2em;
  background-color: #1989fa;
  margin-left: 2px;
  animation: blink 1s infinite;
  vertical-align: text-bottom;
}

@keyframes blink {
  0%, 50% {
    opacity: 1;
  }
  51%, 100% {
    opacity: 0;
  }
}

/* 卡片容器样式已内联到具体元素中 */
</style>
